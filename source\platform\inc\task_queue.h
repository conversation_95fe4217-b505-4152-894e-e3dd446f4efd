#ifndef _TASK_QUEUE_H_
#define _TASK_QUEUE_H_

#include "common.h"
#include "bi.h"
#include "ana.h"
#include "setting.h"
#include "eventRefTable.h"
#include "sample_task.h"

typedef struct
{
	void(*run)(void *dp);
	void * dp;
}s_modQueue;

int addTaskLevel2(void(*run)(void *dp), void *dp);
int addTaskLevel1(void(*run)(void *dp), void *dp);
void runTaskLevel2(void);
void runTaskLevel1(void);

void Bo_Ena(void);
void Bo_DisEna(void);
void bo_on(uint64_t do_state);
void bo_off(void);
void bo_on_single(unsigned char i);
void bo_off_single(unsigned char i);
#endif
