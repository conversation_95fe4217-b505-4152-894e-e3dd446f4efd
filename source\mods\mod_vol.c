#include "mod_vol.h"

void runModVol(s_modVol* dp)
{
	int starptr;
	int starptrUx;
	float tmpVector[2];
	char i;
	unsigned short points;
    unsigned short pointsUx;
	float fm,fx;
	
	//UA UB UC U0
	fm = *dp->F1;
	fx = *dp->F2;
	if( fm > 55 || fm < 45)
		points = SAMPPOINT; 
	else
		points =  50*SAMPPOINT/fm + 0.5;
	
	starptr = circPre(*dp->calPtr, ADBUFLEN, points + 1);			//往前推周期点 采用了emc算法所以点数 + 1
	
	if(fx > 55 || fx < 45)
		pointsUx = SAMPPOINT; 
	else
		pointsUx =  50*SAMPPOINT/fx + 0.5;
	
	starptrUx = circPre(*dp->calPtr, ADBUFLEN, pointsUx + 1);	//往前推周期点 采用了emc算法所以点数 + 1
	
	// 计算基波分量
	for (i = 0; i < CHAN_NUMS; i++)
	{
		if(i != 4)	
			DFT(g_adBuffer[dp->chanNo[i]], ADBUFLEN, starptr, fm, points, tmpVector, 1);
		else
			DFT(g_adBuffer[dp->chanNo[i]], ADBUFLEN, starptrUx, fx, pointsUx, tmpVector, 1);
		
		dp->VECTOR_f[i*2]	= (tmpVector[0] * dp->precisionPara[i][0] - tmpVector[1] * dp->precisionPara[i][1]);		//实部
		dp->VECTOR_f[i*2+1]	= (tmpVector[0] * dp->precisionPara[i][1] + tmpVector[1] * dp->precisionPara[i][0]);	//虚部
	}
	vectPToPP(&dp->VECTOR_f[2* CHAN_NUMS], &dp->VECTOR_f[2*0]);					//求ab bc ca
	phaseToSequence(&dp->VECTOR_f[2 * (CHAN_NUMS + 3)], &dp->VECTOR_f[2 * 0]);	//求序分量 0 1 2
	getMod(&dp->value_f[0], &dp->VECTOR_f[0], FOURIER_NUM);						//幅值

#if 1
	// 计算3次谐波分量
	for (i = 0; i < CHAN_NUMS; i++)
	{
		if(i != 4)
			DFT_Harmonic(g_adBuffer[dp->chanNo[i]], ADBUFLEN, starptr, fm, points, tmpVector, 3);
		else
			DFT_Harmonic(g_adBuffer[dp->chanNo[i]], ADBUFLEN, starptrUx, fx, pointsUx, tmpVector, 3);

		dp->VECTOR_f_h3[i*2]	= (tmpVector[0] * dp->precisionPara[i][0] - tmpVector[1] * dp->precisionPara[i][1]);		//实部
		dp->VECTOR_f_h3[i*2+1]	= (tmpVector[0] * dp->precisionPara[i][1] + tmpVector[1] * dp->precisionPara[i][0]);	//虚部
	}

	// 计算3次谐波幅值
	getMod(&dp->value_f_h3[0], &dp->VECTOR_f_h3[0], CHAN_NUMS);
#endif

	//输出
	for(i = 0; i < FOURIER_NUM; i++)
	{
		dp->VECTOR[i*2] = dp->VECTOR_f[i*2]*DEFAULT_DIV_VAL;
		dp->VECTOR[i*2 + 1] = dp->VECTOR_f[i*2 + 1]*DEFAULT_DIV_VAL;
		dp->value[i] = dp->value_f[i] * DEFAULT_DIV_VAL;
		
		//用于显示的一次值计算
		if(i == 3)
			dp->value_prim[i] = dp->value[i]*dp->sndToPrimU0;
		else if(i == 4)
			dp->value_prim[i] = dp->value[i]*dp->sndToPrimUx;		
		else
			dp->value_prim[i] = dp->value[i]*dp->sndToPrimUn;	
	}

#if 1
	// 处理3次谐波输出
	for(i = 0; i < CHAN_NUMS; i++)
	{
		dp->value_h3[i] = dp->value_f_h3[i] * DEFAULT_DIV_VAL;

		//用于显示的3次谐波一次值计算
		if(i == 3)
			dp->value_prim_h3[i] = dp->value_h3[i]*dp->sndToPrimU0;
		else if(i == 4)
			dp->value_prim_h3[i] = dp->value_h3[i]*dp->sndToPrimUx;
		else
			dp->value_prim_h3[i] = dp->value_h3[i]*dp->sndToPrimUn;
	}
#endif
	dp->uppMax = getMax3Ptr(&dp->value[5]);
	dp->uppMin = getMin3Ptr(&dp->value[5]);
	
	return;
}

int initModVol(s_modVol* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量

	//注册模拟量 这里都设的是4位小数
	regANA(dpName, "ua",	0, &dp->value[0], DEFAULT_POINT, "V");
	regANA(dpName, "ub",	0, &dp->value[1], DEFAULT_POINT, "V");
	regANA(dpName, "uc",	0, &dp->value[2], DEFAULT_POINT, "V");
	regANA(dpName, "u0",	0, &dp->value[3], DEFAULT_POINT, "V");
	regANA(dpName, "ux",	0, &dp->value[4], DEFAULT_POINT, "V");
	regANA(dpName, "uab",0, &dp->value[5], DEFAULT_POINT, "V");
	regANA(dpName, "ubc",0, &dp->value[6], DEFAULT_POINT, "V");
	regANA(dpName, "uca",0, &dp->value[7], DEFAULT_POINT, "V");
	regANA(dpName, "3u0",0, &dp->value[8], DEFAULT_POINT, "V");
	regANA(dpName, "u1",	0, &dp->value[9], DEFAULT_POINT, "V");
	regANA(dpName, "u2",	0, &dp->value[10], DEFAULT_POINT, "V");

	regANA(dpName, "ua_prim",	0, &dp->value_prim[0], DEFAULT_POINT, "KV");
	regANA(dpName, "ub_prim",	0, &dp->value_prim[1], DEFAULT_POINT, "KV");
	regANA(dpName, "uc_prim",	0, &dp->value_prim[2], DEFAULT_POINT, "KV");
	regANA(dpName, "u0_prim",	0, &dp->value_prim[3], DEFAULT_POINT, "KV");
	regANA(dpName, "ux_prim",	0, &dp->value_prim[4], DEFAULT_POINT, "KV");
	regANA(dpName, "uab_prim",   0, &dp->value_prim[5], DEFAULT_POINT, "KV");
	regANA(dpName, "ubc_prim",	0, &dp->value_prim[6], DEFAULT_POINT, "KV");
	regANA(dpName, "uca_prim",	0, &dp->value_prim[7], DEFAULT_POINT, "KV");
	regANA(dpName, "3u0_prim",	0, &dp->value_prim[8], DEFAULT_POINT, "KV");
	regANA(dpName, "u1_prim",	0, &dp->value_prim[9], DEFAULT_POINT, "KV");
	regANA(dpName, "u2_prim",	0, &dp->value_prim[10], DEFAULT_POINT, "KV");

	// 注册3次谐波模拟量
	regANA(dpName, "ua_h3",	0, &dp->value_h3[0], DEFAULT_POINT, "V");
	regANA(dpName, "ub_h3",	0, &dp->value_h3[1], DEFAULT_POINT, "V");
	regANA(dpName, "uc_h3",	0, &dp->value_h3[2], DEFAULT_POINT, "V");
	regANA(dpName, "u0_h3",	0, &dp->value_h3[3], DEFAULT_POINT, "V");
	regANA(dpName, "ux_h3",	0, &dp->value_h3[4], DEFAULT_POINT, "V");

	regANA(dpName, "ua_h3_prim",	0, &dp->value_prim_h3[0], DEFAULT_POINT, "KV");
	regANA(dpName, "ub_h3_prim",	0, &dp->value_prim_h3[1], DEFAULT_POINT, "KV");
	regANA(dpName, "uc_h3_prim",	0, &dp->value_prim_h3[2], DEFAULT_POINT, "KV");
	regANA(dpName, "u0_h3_prim",	0, &dp->value_prim_h3[3], DEFAULT_POINT, "KV");
	regANA(dpName, "ux_h3_prim",	0, &dp->value_prim_h3[4], DEFAULT_POINT, "KV");

	//加入队列
	addTaskLevel2(runModVol, dp);
	return SUCC;
}

int initModParmVol(s_modVol* dp)
{
	float amp_coeff, angle_coeff;
	
	dp->sys_un_snd = getSetByDesc("sys_un_snd");
	dp->sys_un_prim = getSetByDesc("sys_un_prim");
	dp->sys_ux_snd = getSetByDesc("sys_ux_snd");
	dp->sys_ux_prim = getSetByDesc("sys_ux_prim");
	dp->sys_u0_snd = getSetByDesc("sys_u0_snd");
	dp->sys_u0_prim = getSetByDesc("sys_u0_prim");	
	
	//系数除1000因为一次值单位KV
	dp->sndToPrimUn = (float)dp->sys_un_prim/(float)dp->sys_un_snd /1000;	
	dp->sndToPrimUx = (float)dp->sys_ux_prim/(float)dp->sys_ux_snd /1000;	
	dp->sndToPrimU0 = (float)dp->sys_u0_prim/(float)dp->sys_u0_snd /1000;	

	//获取幅值和角度修正定值 角度需要转成弧度
	amp_coeff   = (float)(getSetByDesc("Ua_coeff"))/DEFAULT_DIV_VAL;
	angle_coeff = (float)(getSetByDesc("Ua_angle")) * PI / (180*DEFAULT_DIV_VAL);	
	dp->precisionPara[0][0] =  (cos(angle_coeff)*amp_coeff) / DIG_Un;
	dp->precisionPara[0][1] =  (sin(angle_coeff)*amp_coeff) / DIG_Un;

	amp_coeff	= (float)(getSetByDesc("Ub_coeff"))/DEFAULT_DIV_VAL;
	angle_coeff = (float)(getSetByDesc("Ub_angle")) * PI / (180*DEFAULT_DIV_VAL);
	dp->precisionPara[1][0] = (cos(angle_coeff)*amp_coeff) / DIG_Un;
	dp->precisionPara[1][1] = (sin(angle_coeff)*amp_coeff) / DIG_Un;

	amp_coeff	= (float)(getSetByDesc("Uc_coeff"))/DEFAULT_DIV_VAL;
	angle_coeff = (float)(getSetByDesc("Uc_angle")) * PI / (180*DEFAULT_DIV_VAL);
	dp->precisionPara[2][0] = (cos(angle_coeff)*amp_coeff) / DIG_Un;
	dp->precisionPara[2][1] = (sin(angle_coeff)*amp_coeff) / DIG_Un;

	amp_coeff = (float)(getSetByDesc("U0_coeff"))/DEFAULT_DIV_VAL;
	angle_coeff = (float)(getSetByDesc("U0_angle")) * PI / (180*DEFAULT_DIV_VAL);
	dp->precisionPara[3][0] = (cos(angle_coeff)*amp_coeff) / DIG_U0;
	dp->precisionPara[3][1] = (sin(angle_coeff)*amp_coeff) / DIG_U0;

	amp_coeff = (float)(getSetByDesc("Ux_coeff"))/DEFAULT_DIV_VAL;
	angle_coeff = (float)(getSetByDesc("Ux_angle")) * PI / (180*DEFAULT_DIV_VAL);
	dp->precisionPara[4][0] = (cos(angle_coeff)*amp_coeff) / DIG_Ux;
	dp->precisionPara[4][1] = (sin(angle_coeff)*amp_coeff) / DIG_Ux;
	return SUCC;
}
