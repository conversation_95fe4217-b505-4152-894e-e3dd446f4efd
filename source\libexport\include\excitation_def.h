/**
 * @file excitation_def.h
 * @brief 励磁通信模块公共定义文件
 * @date 2023-09-15
 */

#ifndef __EXCITATION_DEF_H__
#define __EXCITATION_DEF_H__

#include <stdio.h>
#include <stdlib.h>
#include    "DATATypeDEF.H"
#include    "xsj_lib.h"
#include    "bi.h"
#include    "setting.h"

#include	"s7kmCommon.h"
#include	"Excitation.h"
#include	"s7kmSend.h"
#include	"s7kmReceive.h"
#include	"Serial7000Master.h"

#define LCDATA //!!!

// 通信命令号的定义
#define		COMNUM_READVERSION		0X01	/*读版本信息*/
#define		COMNUM_ANALOG			0X02	/*读连续模拟型数据*/
#define		COMNUM_SWITCH			0X04	/*读连续开关型数据*/
#define		COMNUM_CALIBRATE		0X06	/*读写模拟量校准值*/
#define		COMNUM_ENERY			0X0A	/*读电度量数据/修改电度底度*/
#define		COMNUM_SINGLESETTINGS	0X0B	/*读写单个定值*/
#define		COMNUM_SETTINGS			0X0C	/*读写定值区的分组定值*/
#define		COMNUM_EXSETTINGAREA	0X0D	/*切换运行定值区*/
#define		COMNUM_READSOE			0X0E	/*读SOE*/
#define		COMNUM_CONTROL			0X19	/*控制和设定操作(有返校和不返校两种模式)*/
#define		COMNUM_TIMING			0X1A	/*校时与读从站时间(支持广播校时)*/
#define		COMNUM_MANUALSTARTWAVE	0X1F	/*手动启动录波*/
#define		COMNUM_READWAVE			0X20	/*读录波数据*/
#define		COMNUM_AUTOAMEND		0X22	/*启动自动校正通道*/
#define		COMNUM_FARNEAR			0X23	/*远近控*/
#define		COMNUM_REBACK			0X24	/*复归命令*/
#define		COMNUM_RESET			0X25	/*单元复位*/
#define		COMNUM_OUPORTDRIVE		0X26	/*出口传动*/

//操作源定义
#define		OPSRC_EXTERN	0X01			//外部
#define		OPSRC_UNIT		0X02			//单元 //???显示
#define		OPSRC_HOST		0X04			//后台 //后台
#define		OPSRC_PROTECT	0X08			//保护
#define		OPSRC_MONITOR	0X10			//监控
#define		OPSRC_ATTEMPER	0X20			//远方
#define		OPSRC_ZB		0x40			//就地 //???FRAM

//读写控制字
//控制命令
#define		CTLCMD_READ			0X55
#define		CTLCMD_WRITE		0XAA
#define		CTLCMD_CANCLE		0XCC

#define LC1_GRPNO	101	//励磁定值区组号
#define LC2_GRPNO	102	//励磁定值区组号
#define LC3_GRPNO	103	//励磁定值区组号

//定值内外组号映射   在can通信时，采用组号对应定值的方式，此处需知道配置的组号才能正确的搜索到具体定值对象。
#define     USE_COMM_DZ_GRP_NO    12    //最大通信定值组号
extern const uint8 GDZIn_OutIndex[USE_COMM_DZ_GRP_NO][2];
#endif /* __EXCITATION_DEF_H__ */ 